export function useVideoResolution() {
  const { model } = useVideoGenModels()
  // Resolution based on model
  const resolution = useCookie<string>('video-gen-resolution', {
    default: () => model.value?.resolutions?.default || '720p'
  })

  // Resolution options based on current model
  const resolutionOptions = computed(() => {
    const options = model.value?.resolutions?.options || []
    return options.map((res: string) => ({
      label: res,
      value: res,
      description: res === '720p' ? 'HD Quality' : 'Full HD Quality'
    }))
  })

  // Check if resolution selection is available for current model
  const isResolutionSelectable = computed(() => {
    return model.value?.options?.includes('resolution') && resolutionOptions.value.length > 0
  })

  // Watch for model changes and update resolution if needed
  watch(() => model.value, (newModel) => {
    if (newModel?.resolutions?.default && !newModel.resolutions.options.includes(resolution.value)) {
      resolution.value = newModel.resolutions.default
    }
  })

  return {
    resolution,
    resolutionOptions,
    isResolutionSelectable
  }
}
